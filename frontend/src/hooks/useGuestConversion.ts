import { useState, useCallback } from 'react';
import { useAuth, useClerk } from '@clerk/nextjs';
import { convertGuestUser, GuestConvertRequest } from '@/api/user';
import { useAuthStore } from '@/stores/auth.store';
import { useGuestAuth } from './useGuestAuth';
import { useRedirectQueryUrl } from './useRedirectQueryUrl';
import { create } from 'zustand';

interface ConversionState {
  isConverting: boolean;
  setIsConverting: (converting: boolean) => void;
}

export const useConversionState = create<ConversionState>((set) => ({
  isConverting: false,
  setIsConverting: (converting: boolean) => set({ isConverting: converting }),
}));

let isConversionInProgress = false;

export const setConversionInProgress = (value: boolean) => {
  isConversionInProgress = value;
};

export const getConversionInProgress = () => {
  return isConversionInProgress;
};

interface UseGuestConversionReturn {
  convertGuest: (data: GuestConvertRequest) => Promise<boolean>;
  isConverting: boolean;
  error: string | null;
  currentStep: 'idle' | 'converting' | 'complete';
}

export const useGuestConversion = (): UseGuestConversionReturn => {
  const [isConverting, setIsConverting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<'idle' | 'converting' | 'complete'>('idle');
  const { isSignedIn } = useAuth();
  const clerk = useClerk();
  const { clearGuestAuth } = useAuthStore();
  const { getValidGuestToken } = useGuestAuth();
  const { clearRedirectUrl } = useRedirectQueryUrl();
  const { setIsConverting: setGlobalConverting } = useConversionState();

  const convertGuest = useCallback(
    async (data: GuestConvertRequest): Promise<boolean> => {
      if (isSignedIn) {
        // Already authenticated, no need to convert
        return true;
      }

      setIsConverting(true);
      setGlobalConverting(true);
      setError(null);
      setCurrentStep('converting');

      try {
        // Step 1: Send /user/guest/convert/ endpoint with first name, last name, email data
        const guestToken = getValidGuestToken();
        if (!guestToken) {
          throw new Error('No guest token available. Please refresh the page and try again.');
        }

        const response = await convertGuestUser({
          request: data,
          token: guestToken,
        });

        if (!response.signInToken || !response.clerkId) {
          throw new Error('Failed to convert guest user - invalid response from server');
        }

        // Use the signInToken from backend to authenticate with Clerk
        const signInResult = await clerk.client.signIn.create({
          strategy: 'ticket',
          ticket: response.signInToken,
        });

        if (signInResult.status === 'complete') {
          await clerk.setActive({ session: signInResult.createdSessionId });

          // Set visitedOnboarding flag to prevent redirect to onboarding
          try {
            await new Promise((resolve) => setTimeout(resolve, 100));
            const currentUser = clerk.user;
            if (currentUser) {
              await currentUser.update({
                unsafeMetadata: {
                  ...currentUser.unsafeMetadata,
                  visitedOnboarding: true,
                },
              });
              await new Promise((resolve) => setTimeout(resolve, 200));
            }
          } catch (metadataError) {
            console.error('Error setting visitedOnboarding flag:', metadataError);
          }
        } else {
          throw new Error('Sign-in not completed');
        }

        clearGuestAuth();
        clearRedirectUrl();

        setCurrentStep('complete');
        setGlobalConverting(false);
        return true;
      } catch (err) {
        let errorMessage = 'Failed to convert guest user';
        if (err instanceof Error) {
          errorMessage = err.message;

          // Special handling for already converted guest
          if (err.message.includes('Guest user already has a Clerk ID')) {
            clearGuestAuth();
            clearRedirectUrl();
            setCurrentStep('complete');
            setGlobalConverting(false);
            return true;
          }
        }

        // Check if it's a network error
        if (err && typeof err === 'object' && 'response' in err) {
          const axiosError = err as any;

          if (
            axiosError.response?.status === 400 &&
            axiosError.response?.data?.detail?.includes('Guest user already has a Clerk ID')
          ) {
            clearGuestAuth();
            clearRedirectUrl();
            setCurrentStep('complete');
            setGlobalConverting(false);
            return true;
          }
        }

        setError(errorMessage);
        setCurrentStep('idle');
        return false;
      } finally {
        setIsConverting(false);
        setGlobalConverting(false);
      }
    },
    [isSignedIn, clerk, clearGuestAuth, getValidGuestToken, clearRedirectUrl, setGlobalConverting]
  );

  return {
    convertGuest,
    isConverting,
    error,
    currentStep,
  };
};
