import { useState, useEffect, useCallback } from 'react';

export interface ConvertedUserData {
  clerkId: string;
  userId: number;
  signInToken: string;
  convertedAt: number;
  visitedOnboarding: boolean;
}

export const useConvertedUser = () => {
  const [convertedUser, setConvertedUser] = useState<ConvertedUserData | null>(null);

  // Load converted user data from localStorage
  useEffect(() => {
    const savedData = localStorage.getItem('convertedUser');
    if (savedData) {
      try {
        const userData = JSON.parse(savedData) as ConvertedUserData;
        setConvertedUser(userData);
      } catch (error) {
        console.error('Error parsing converted user data:', error);
        localStorage.removeItem('convertedUser');
      }
    }
  }, []);

  // Check if user is converted (has local data)
  const isConverted = useCallback(() => {
    return convertedUser !== null;
  }, [convertedUser]);

  // Get converted user data
  const getConvertedUser = useCallback(() => {
    return convertedUser;
  }, [convertedUser]);

  // Clear converted user data
  const clearConvertedUser = useCallback(() => {
    localStorage.removeItem('convertedUser');
    setConvertedUser(null);
  }, []);

  // Update converted user data
  const updateConvertedUser = useCallback((updates: Partial<ConvertedUserData>) => {
    if (convertedUser) {
      const updatedUser = { ...convertedUser, ...updates };
      localStorage.setItem('convertedUser', JSON.stringify(updatedUser));
      setConvertedUser(updatedUser);
    }
  }, [convertedUser]);

  return {
    convertedUser,
    isConverted,
    getConvertedUser,
    clearConvertedUser,
    updateConvertedUser,
  };
};
