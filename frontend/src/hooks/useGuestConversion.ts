import { useState, useCallback } from 'react';
import { useAuth, useClerk } from '@clerk/nextjs';
import { convertGuestUser, GuestConvertRequest } from '@/api/user';
import { useAuthStore } from '@/stores/auth.store';
import { useGuestAuth } from './useGuestAuth';
import { useRedirectQueryUrl } from './useRedirectQueryUrl';
import { create } from 'zustand';

// Simple store to track conversion state
interface ConversionState {
  isConverting: boolean;
  setIsConverting: (converting: boolean) => void;
}

export const useConversionState = create<ConversionState>((set) => ({
  isConverting: false,
  setIsConverting: (converting: boolean) => set({ isConverting: converting }),
}));

// Global state to track conversion process
let isConversionInProgress = false;

export const setConversionInProgress = (value: boolean) => {
  isConversionInProgress = value;
};

export const getConversionInProgress = () => {
  return isConversionInProgress;
};

interface UseGuestConversionReturn {
  convertGuest: (data: GuestConvertRequest) => Promise<boolean>;
  isConverting: boolean;
  error: string | null;
  currentStep: 'idle' | 'converting' | 'signing-in' | 'complete';
}

export const useGuestConversion = (): UseGuestConversionReturn => {
  const [isConverting, setIsConverting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<'idle' | 'converting' | 'signing-in' | 'complete'>(
    'idle'
  );
  const { isSignedIn } = useAuth();
  const clerk = useClerk();
  const { clearGuestAuth } = useAuthStore();
  const { getValidGuestToken } = useGuestAuth();
  const { clearRedirectUrl } = useRedirectQueryUrl();
  const { setIsConverting: setGlobalConverting } = useConversionState();

  const convertGuest = useCallback(
    async (data: GuestConvertRequest): Promise<boolean> => {
      if (isSignedIn) {
        // Already authenticated, no need to convert
        return true;
      }

      setIsConverting(true);
      setGlobalConverting(true); // Set global flag to prevent layout redirects
      setError(null);
      setCurrentStep('converting');
      setConversionInProgress(true);

      try {
        // Step 1: Send /user/guest/convert/ endpoint with first name, last name, email data
        const guestToken = getValidGuestToken();
        if (!guestToken) {
          throw new Error('No guest token available. Please refresh the page and try again.');
        }

        const response = await convertGuestUser({
          request: data,
          token: guestToken,
        });

        if (!response.signInToken || !response.clerkId) {
          throw new Error('Failed to convert guest user - invalid response from server');
        }

        // Step 2: Generate a sign-in token by Clerk documentation and save this token using Clerk SDK
        setCurrentStep('signing-in');
        const signInResult = await clerk.client.signIn.create({
          strategy: 'ticket',
          ticket: response.signInToken,
        });

        if (signInResult.status === 'complete') {
          await clerk.setActive({ session: signInResult.createdSessionId });

          try {
            // Wait for session to be fully established
            await new Promise((resolve) => setTimeout(resolve, 100));

            const currentUser = clerk.user;
            if (currentUser) {
              await currentUser.update({
                unsafeMetadata: {
                  ...currentUser.unsafeMetadata,
                  visitedOnboarding: true,
                },
              });

              // Wait a bit more for the metadata update to propagate
              await new Promise((resolve) => setTimeout(resolve, 200));
              console.log('visitedOnboarding flag set, metadata should be updated');
            }
          } catch (metadataError) {
            console.error('Error setting visitedOnboarding flag:', metadataError);
          }
        } else {
          throw new Error('Sign-in not completed');
        }

        // Clear guest authentication state since user is now authenticated
        console.log('Clearing guest auth and redirect URL to prevent navigation');
        clearGuestAuth();

        // Clear any redirect URL to prevent AuthRedirectHandler from redirecting
        clearRedirectUrl();
        console.log('Guest conversion completed successfully, staying on current page');

        setCurrentStep('complete');
        setGlobalConverting(false); // Clear global flag
        return true;
      } catch (err) {
        console.error('Guest conversion error:', err);
        let errorMessage = 'Failed to convert guest user';
        if (err instanceof Error) {
          errorMessage = err.message;
          console.error('Error message:', err.message);
          console.error('Error stack:', err.stack);

          // Special handling for already converted guest
          if (err.message.includes('Guest user already has a Clerk ID')) {
            clearGuestAuth();
            clearRedirectUrl(); // Prevent redirect
            setCurrentStep('complete');
            setGlobalConverting(false); // Clear global flag
            return true; // Treat as success since user is already converted
          }
        }

        // Check if it's a network error
        if (err && typeof err === 'object' && 'response' in err) {
          const axiosError = err as any;
          console.error('HTTP Status:', axiosError.response?.status);
          console.error('Response data:', axiosError.response?.data);
          console.error('Request config:', axiosError.config);

          // Handle specific server error for already converted guest
          if (
            axiosError.response?.status === 400 &&
            axiosError.response?.data?.detail?.includes('Guest user already has a Clerk ID')
          ) {
            console.log(
              'Guest user already converted (from server response), clearing guest state'
            );
            clearGuestAuth();
            clearRedirectUrl(); // Prevent redirect
            setCurrentStep('complete');
            setGlobalConverting(false); // Clear global flag
            return true; // Treat as success since user is already converted
          }
        }

        setError(errorMessage);
        setCurrentStep('idle');
        return false;
      } finally {
        setIsConverting(false);
        setGlobalConverting(false); // Always clear global flag
      }
    },
    [isSignedIn, clerk, clearGuestAuth, getValidGuestToken, clearRedirectUrl, setGlobalConverting]
  );

  return {
    convertGuest,
    isConverting,
    error,
    currentStep,
  };
};
