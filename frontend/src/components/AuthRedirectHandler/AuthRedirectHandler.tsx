import { useEffect } from 'react';
import { useAuth, useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { useRedirectQueryUrl } from '@/hooks/useRedirectQueryUrl';

export const AuthRedirectHandler = () => {
  const { isSignedIn, isLoaded } = useAuth();
  const { user } = useUser();
  const router = useRouter();
  const { redirectUrl, clearRedirectUrl } = useRedirectQueryUrl();

  useEffect(() => {
    console.log('AuthRedirectHandler effect triggered:', {
      isLoaded,
      isSignedIn,
      redirectUrl,
      hasCompletedOnboarding: user?.unsafeMetadata?.visitedOnboarding === true,
      currentPath: window.location.pathname + window.location.search,
    });

    if (isLoaded && isSignedIn) {
      const savedUrl = redirectUrl;
      const hasCompletedOnboarding = user?.unsafeMetadata?.visitedOnboarding === true;

      if (savedUrl) {
        console.log('AuthRedirectHandler: Found saved URL:', savedUrl);
        try {
          const url = new URL(savedUrl);
          const redirectPath = url.pathname + url.search;

          if (hasCompletedOnboarding) {
            console.log(
              'AuthRedirectHandler: User has completed onboarding, clearing redirect URL'
            );
            clearRedirectUrl();

            if (redirectPath !== window.location.pathname + window.location.search) {
              console.log('AuthRedirectHandler: Redirecting to:', redirectPath);
              router.replace(redirectPath);
            } else {
              console.log('AuthRedirectHandler: Already on target path, no redirect needed');
            }
          } else {
            console.log('AuthRedirectHandler: User has not completed onboarding, not redirecting');
          }
        } catch (error) {
          console.error('Error parsing saved redirect URL:', error);
          if (hasCompletedOnboarding) {
            clearRedirectUrl();
          }
        }
      } else {
        console.log('AuthRedirectHandler: No saved URL found');
      }
    }
  }, [isLoaded, isSignedIn, user, router, redirectUrl, clearRedirectUrl]);

  return null;
};
